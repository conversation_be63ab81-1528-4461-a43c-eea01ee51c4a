'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Heart, MessageCircle, X, Check, Users, Send, Inbox, UserCheck, AlertCircle, Loader2 } from "lucide-react";

interface MatchDetails {
  extraversion: number;
  agreeableness: number;
  conscientiousness: number;
  negativeEmotionality: number;
  openMindedness: number;
  selfDisclosure: number;
}

interface User {
  id: string;
  name: string;
  image: string | null;
  religion: string;
  gender: string;
  age: number;
  occupation: string;
  isSmoker: boolean;
  acceptDifferentReligion: boolean;
  about: string;
}

interface Match {
  id: string;
  user: User;
  matchScore: number;
  matchDetails: MatchDetails;
  interpretation: string;
  invitedBy: string | null;
  acceptedBy: string | null;
  rejectedBy: string | null;
  status: 'pending' | 'invited' | 'accepted' | 'rejected' | 'expired';
  isConnected: boolean;
}

type InvitationType = 'all' | 'incoming' | 'outgoing' | 'connected';

interface MatchesResponse {
  data: Match[];
  totalItems?: number;
  itemsPerPage?: number;
  totalPages?: number;
  currentPage?: number;
}

export default function MatchesPage() {
  const [matches, setMatches] = useState<Match[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<InvitationType>('all');
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    fetchMatches(activeTab);
  }, [activeTab]);

  useEffect(() => {
    // Get current user ID from profile or token
    getCurrentUserId();
  }, []);

  const getCurrentUserId = async () => {
    try {
      const token = localStorage.getItem('pairsona_token');
      if (!token) return;

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const profile = await response.json();
        setCurrentUserId(profile.id);
      }
    } catch (err) {
      console.error('Failed to get current user ID:', err);
    }
  };

  const fetchMatches = async (type: InvitationType = 'all') => {
    try {
      setLoading(true);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const params = new URLSearchParams();
      if (type !== 'all') {
        params.append('type', type);
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match?${params.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('pairsona_token');
          router.replace('/login');
          return;
        }
        throw new Error('Failed to fetch matches');
      }

      const data: MatchesResponse = await response.json();
      console.log('Matches data from API:', data);
      setMatches(data.data || data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleMatchAction = async (matchId: string, action: 'invite' | 'accept' | 'reject') => {
    try {
      console.log(`Attempting to ${action} match with ID:`, matchId);
      setActionLoading(matchId);
      const token = localStorage.getItem('pairsona_token');
      if (!token) {
        router.replace('/login');
        return;
      }

      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/match/${matchId}/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to ${action} match`);
      }

      // Refresh matches to get updated data
      await fetchMatches(activeTab);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setActionLoading(null);
    }
  };



  const getMatchType = (match: Match): 'can_invite' | 'incoming' | 'outgoing' | 'connected' | 'rejected' => {
    if (match.isConnected) return 'connected';
    if (match.status === 'rejected') return 'rejected';
    if (match.status === 'invited') {
      if (match.invitedBy === currentUserId) return 'outgoing';
      return 'incoming';
    }
    return 'can_invite';
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  const getScoreColor = (score: number) => {
    // Convert to percentage for color logic (score is 0-1 scale)
    const percentage = score * 100;
    if (percentage >= 80) return 'text-green-600 bg-green-50';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const formatMatchScore = (score: number) => {
    // Convert from 0-1 scale to percentage (0-100)
    return Math.round(score * 100);
  };

  const getReligionIcon = (religion: string) => {
    const religionMap: { [key: string]: string } = {
      'islam': '☪️',
      'kristen': '✝️',
      'katolik': '✝️',
      'hindu': '🕉️',
      'buddha': '☸️',
      'konghucu': '☯️'
    };
    return religionMap[religion?.toLowerCase()] || '🙏';
  };

  const renderLoadingState = () => (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Pasangan</h1>
        <p className="text-muted-foreground">Temukan pasangan sempurna berdasarkan psikologi.</p>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all" disabled>
            <Users className="w-4 h-4 mr-2" />
            All
          </TabsTrigger>
          <TabsTrigger value="incoming" disabled>
            <Inbox className="w-4 h-4 mr-2" />
            Incoming
          </TabsTrigger>
          <TabsTrigger value="outgoing" disabled>
            <Send className="w-4 h-4 mr-2" />
            Outgoing
          </TabsTrigger>
          <TabsTrigger value="connected" disabled>
            <UserCheck className="w-4 h-4 mr-2" />
            Terhubung
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={`loading-${i}`} className="overflow-hidden animate-pulse">
                <div className="h-32 bg-gray-200"></div>
                <CardHeader>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 rounded"></div>
                    <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );

  if (loading) {
    return renderLoadingState();
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pasangan</h1>
          <p className="text-muted-foreground">Temukan pasangan sempurna berdasarkan psikologi.</p>
        </div>
        <Card className="p-6">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => fetchMatches(activeTab)}>Coba Lagi</Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Pasangan</h1>
        <p className="text-muted-foreground">Temukan pasangan sempurna berdasarkan psikologi.</p>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as InvitationType)} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="all">
            <Users className="w-4 h-4 mr-2" />
            Semua
          </TabsTrigger>
          <TabsTrigger value="incoming">
            <Inbox className="w-4 h-4 mr-2" />
            Undangan Masuk
          </TabsTrigger>
          <TabsTrigger value="outgoing">
            <Send className="w-4 h-4 mr-2" />
            Undangan Keluar
          </TabsTrigger>
          <TabsTrigger value="connected">
            <UserCheck className="w-4 h-4 mr-2" />
            Terhubung
          </TabsTrigger>
        </TabsList>

        {(['all', 'incoming', 'outgoing', 'connected'] as InvitationType[]).map((tabType) => (
          <TabsContent key={tabType} value={tabType} className="mt-6">
            {matches.length === 0 ? (
              <Card className="p-6">
                <div className="text-center">
                  <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {tabType === 'all' && 'No matches yet'}
                    {tabType === 'incoming' && 'No incoming invitations'}
                    {tabType === 'outgoing' && 'No outgoing invitations'}
                    {tabType === 'connected' && 'Belum ada koneksi'}
                  </h3>
                  <p className="text-gray-500">
                    {tabType === 'all' && 'Lengkapi tes kepribadian Anda untuk menemukan pasangan yang sempurna!'}
                    {tabType === 'incoming' && 'Belum ada yang mengundang Anda. Tetap aktifkan profil Anda!'}
                    {tabType === 'outgoing' && 'Anda belum mengirim undangan. Mulai jelajahi!'}
                    {tabType === 'connected' && 'Terima beberapa undangan untuk mulai terhubung!'}
                  </p>
                </div>
              </Card>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {matches.map((match) => {
                  const matchType = getMatchType(match);

                  return (
                    <Card key={match.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                      <div className="h-32 bg-[#F2E7DB] flex items-center justify-center relative">
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={match.user.image || undefined} alt={match.user.name} />
                          <AvatarFallback className="bg-[#D0544D]/20 text-[#D0544D] text-lg font-bold">
                            {getInitials(match.user.name)}
                          </AvatarFallback>
                        </Avatar>
                        <Badge
                          className={`absolute top-4 right-4 ${getScoreColor(match.matchScore)}`}
                          variant="secondary"
                        >
                          {formatMatchScore(match.matchScore)}%
                        </Badge>
                      </div>

                      <CardHeader className="pb-3">
                        <CardTitle className="flex items-center justify-between">
                          <span>{match.user.name}</span>
                          <div className="flex gap-1">
                            {match.isConnected && (
                              <Badge className="bg-green-100 text-green-800">Terhubung</Badge>
                            )}
                            {matchType === 'incoming' && (
                              <Badge className="bg-blue-100 text-blue-800">Mengundang Anda</Badge>
                            )}
                            {matchType === 'outgoing' && (
                              <Badge className="bg-yellow-100 text-yellow-800">Waiting</Badge>
                            )}
                            {matchType === 'rejected' && (
                              <Badge className="bg-red-100 text-red-800">Ditolak</Badge>
                            )}
                          </div>
                        </CardTitle>
                        <CardDescription>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2 text-sm">
                              <span>{match.user.age} tahun</span>
                              <span>•</span>
                              <span>{match.user.occupation}</span>
                              <span>•</span>
                              <span className="flex items-center gap-1">
                                {getReligionIcon(match.user.religion)}
                                {match.user.religion}
                              </span>
                            </div>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <span>{match.user.isSmoker ? '🚬 Smoker' : '🚭 Non-smoker'}</span>
                              <span>•</span>
                              <span>{match.user.acceptDifferentReligion ? '🤝 Terbuka untuk agama berbeda' : '🙏 Lebih suka agama yang sama'}</span>
                            </div>
                            {match.user.about && (
                              <p className="text-sm text-gray-600 italic line-clamp-2">"{match.user.about}"</p>
                            )}
                            <p className="text-sm font-medium text-[#D0544D]">{match.interpretation}</p>
                          </div>
                        </CardDescription>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex gap-2">
                            {matchType === 'can_invite' && (
                              <Button
                                onClick={() => handleMatchAction(match.id, 'invite')}
                                disabled={actionLoading === match.id}
                                className="flex-1 bg-[#D0544D] hover:bg-[#D0544D]/90 text-white"
                                size="sm"
                              >
                                {actionLoading === match.id ? (
                                  <>
                                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                    Inviting...
                                  </>
                                ) : (
                                  <>
                                    <Heart className="w-4 h-4 mr-1" />
                                    Undang
                                  </>
                                )}
                              </Button>
                            )}

                            {matchType === 'incoming' && (
                              <>
                                <Button
                                  onClick={() => handleMatchAction(match.id, 'accept')}
                                  disabled={actionLoading === match.id}
                                  className="flex-1 bg-green-600 hover:bg-green-700"
                                  size="sm"
                                >
                                  {actionLoading === match.id ? (
                                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                  ) : (
                                    <Check className="w-4 h-4 mr-1" />
                                  )}
                                  Terima
                                </Button>
                                <Button
                                  onClick={() => handleMatchAction(match.id, 'reject')}
                                  disabled={actionLoading === match.id}
                                  variant="outline"
                                  className="flex-1"
                                  size="sm"
                                >
                                  {actionLoading === match.id ? (
                                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                  ) : (
                                    <X className="w-4 h-4 mr-1" />
                                  )}
                                  Tolak
                                </Button>
                              </>
                            )}

                            {matchType === 'outgoing' && (
                              <Button
                                disabled
                                variant="outline"
                                className="flex-1"
                                size="sm"
                              >
                                <Send className="w-4 h-4 mr-1" />
                                Invitation Sent
                              </Button>
                            )}

                            {matchType === 'connected' && (
                              <Button
                                className="flex-1 bg-[#D0544D] hover:bg-[#D0544D]/90 text-white"
                                size="sm"
                                onClick={() => {
                                  // TODO: Implement chat functionality
                                  console.log('Open chat with:', match.user.name);
                                }}
                              >
                                <MessageCircle className="w-4 h-4 mr-1" />
                                Chat
                              </Button>
                            )}

                            {matchType === 'rejected' && (
                              <Button
                                disabled
                                variant="outline"
                                className="flex-1"
                                size="sm"
                              >
                                <X className="w-4 h-4 mr-1" />
                                Ditolak
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
