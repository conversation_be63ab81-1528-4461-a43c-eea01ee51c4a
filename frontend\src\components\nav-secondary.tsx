"use client"

import * as React from "react"
import { type LucideIcon } from "lucide-react"
import { useRouter } from "next/navigation"
import Cookies from "js-cookie"

import {
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"

export function NavSecondary({
  items,
  ...props
}: {
  items: {
    title: string
    url: string
    icon: LucideIcon
  }[]
} & React.ComponentPropsWithoutRef<typeof SidebarGroup>) {
  const router = useRouter()

  const handleLogout = () => {
    // Clear tokens from localStorage and cookies
    localStorage.removeItem('pairsona_token')
    Cookies.remove('pairsona_token')

    // Redirect to login page
    router.push('/login')
  }

  const handleItemClick = (item: { title: string; url: string; icon: LucideIcon }) => {
    if (item.title === "Keluar") {
      handleLogout()
    } else {
      // For other items, navigate normally
      router.push(item.url)
    }
  }

  return (
    <SidebarGroup {...props}>
      <SidebarGroupContent>
        <SidebarMenu>
          {items.map((item) => (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton
                size="sm"
                className="text-white hover:bg-white/10 hover:text-white cursor-pointer"
                onClick={() => handleItemClick(item)}
              >
                <item.icon />
                <span>{item.title}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  )
}
